# doubt-flow.yml
# Flow - Complete doubt flow: mapping, submission, and teacher verification
config:
  variables:
    question: "what is dc voltage"
    teacher_reply: "DC voltage is the constant, unidirectional flow of electric charge."
  defaults:
    headers:
      accept: application/json
      x-device-id: 649baf75-e974-48ab-90e7-eb418a91a7b0
      x-client-type: web
  fixtures:
    - bffURL
    - testData
    - login

scenarios:
  - name: "Complete doubt flow: mapping, submission, and teacher verification"
    skipInProd: true
    flow: 
      - function:
          name: getFormattedDateTime
          args:
            - "HH:mm:ss"
          capture: current_time
          
      # Step 1: Upload doubt mapping template
      - function:
          name: updateExcelFile
          args:
            - "./test-data/doubt_template.xlsx"
            - "{{testData.batch.id}}"
            - "{{testData.teacher.phone}}"
          capture: excel_update_result
      - function:
          name: uploadExcelFile
          args:
            - "{{bffURL}}/internal-bff/api/v1/resource/doubt-teacher-mapping/upload"
            - "{{testData.env.ADMIN_ACCESS_TOKEN}}"
            - "./test-data/doubt_template.xlsx"
            - "file"
            - 
              course_id: "{{testData.batch.course}}"
              batch_id: "{{testData.batch.id}}"
              center: "{{testData.env.CENTER_ID}}"
          capture: upload_response
      - log: "Doubt mapping template uploaded successfully: {{upload_response}}"

      # Step 2: Submit the doubt from student side
      - post:
          name: "Submit doubt from student"
          url: "{{bffURL}}/api/v1/v6/doubts/submit"
          headers:
            authorization: "Bearer {{login.studentToken}}"
            x-selected-batch-list: "{{testData.batch.id}}"
            x-selected-course-id: "{{testData.batch.course}}"
          json:
            doubt:
              images: []
              override: "false"
              text: "{{question}}? {{current_time}}"
          capture:
            - json: $.data
              as: submit_doubt_response
            - json: $.data.action.data.query.did
              as: doubt_id
          expect:
            - statusCode: 200
            - hasProperty: "data.action.data.query.did"
      - log: "Doubt submitted successfully with ID: {{doubt_id}}"

      # Step 3: Reply to the doubt with "Confirm"
      - post:
          name: "Reply to doubt with confirmation"
          url: "{{bffURL}}/api/v1/v4/doubts/reply"
          headers:
            authorization: "Bearer {{login.studentToken}}"
            x-selected-batch-list: "{{testData.batch.id}}"
            x-selected-course-id: "{{testData.batch.course}}"
          json:
            doubt_id: "{{doubt_id}}"
            doubt_reply:
              text: "Confirm"
              images: []
              audios: []
            meta:
              submission: true
            is_chip_selected: true
          capture:
            - json: $.data.reply_id
              as: reply_id
            - json: $.data.status
              as: doubt_status
          expect:
            - statusCode: 200
            - hasProperty: "data.reply_id"
      - log: "Reply sent with ID: {{reply_id}}, doubt status: {{doubt_status}}"

      - sleep: 5000
      
      # Step 4: Reply with "No, I didn't get it" to transfer to teacher
      - post:
          name: "Reply with 'No, I didn't get it' to transfer to teacher"
          url: "{{bffURL}}/api/v1/v4/doubts/reply"
          headers:
            authorization: "Bearer {{login.studentToken}}"
            x-selected-batch-list: "{{testData.batch.id}}"
            x-selected-course-id: "{{testData.batch.course}}"
          json:
            doubt_id: "{{doubt_id}}"
            doubt_reply:
              text: "No, I need help"
              images: []
              audios: []
            meta: {}
            is_chip_selected: true
          capture:
            - json: $.data.reply_id
              as: transfer_reply_id
            - json: $.data.status
              as: transfer_status
          expect:
            - statusCode: 200
            - hasProperty: "data.reply_id"
      - log: "Transfer reply sent with ID: {{transfer_reply_id}}, doubt status: {{transfer_status}}"

      - sleep: 5000

      # Step 5: Reply with "Ask a teacher"
      - post:
          name: "Reply with 'Ask a teacher'"
          url: "{{bffURL}}/api/v1/v4/doubts/reply"
          headers:
            authorization: "Bearer {{login.studentToken}}"
            x-selected-batch-list: "{{testData.batch.id}}"
            x-selected-course-id: "{{testData.batch.course}}"
          json:
            doubt_id: "{{doubt_id}}"
            doubt_reply:
              text: "Ask a teacher"
              images: []
              audios: []
            meta: {}
            is_chip_selected: true
          capture:
            - json: $.data.reply_id
              as: ask_teacher_reply_id
            - json: $.data.status
              as: ask_teacher_status
          expect:
            - statusCode: 200
            - hasProperty: "data.reply_id"
      - log: "Ask teacher reply sent with ID: {{ask_teacher_reply_id}}, status: {{ask_teacher_status}}"

      - sleep: 10000

      # Step 6: Teacher checks for unresolved doubts count
      - post:
          name: "Teacher checks unresolved doubts count"
          url: "{{bffURL}}/internal-bff/api/v1/doubts/resolvers/count"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: {}
          capture:
            - json: $.data.data.unresolved
              as: unresolved_count
          expect:
            - statusCode: 200
            - hasProperty: "data.data.unresolved"
      - log: "Teacher found {{unresolved_count}} unresolved doubts"

      # Step 7: Teacher retrieves list of unresolved doubts
      - post:
          name: "Teacher retrieves unresolved doubts"
          url: "{{bffURL}}/internal-bff/api/v1/doubts/resolvers/doubts"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            current_page: 1
            filters:
              - filter_name: "STATUS"
                filter_value: "OPEN"
            limit: 10
            next_page: null
            resolver_id: null
            sort_by: null
            token: ""
          capture:
            - json: $.data.doubt
              as: unresolved_doubts
            - json: $.data.total_count
              as: total_doubts
            - json: $.data.doubt[0].id
              as: teacher_doubt_id
          expect:
            - statusCode: 200
            - hasProperty: "data.doubt"
            - hasProperty: "data.total_count"
      - log: "Teacher retrieved {{total_doubts}} unresolved doubts with first doubt ID: {{teacher_doubt_id}}"

      # Step 8: Teacher views the doubt conversation
      - get:
          name: "Teacher views doubt conversation"
          url: "{{bffURL}}/internal-bff/api/v1/v3/doubts/{{teacher_doubt_id}}/conversation"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
            # x-client-type: "web"
            # x-device-id: "649baf75-e974-48ab-90e7-eb418a91a7b0"
            # accept: "application/json"
            # origin: "https://console.allen-stage.in"
            # referer: "https://console.allen-stage.in/"
          capture:
            - json: $.data
              as: teacher_conversation
          expect:
            - statusCode: 200
            - hasProperty: "data.replies"
      - log: "Teacher viewed doubt conversation"

      # Step 9: Teacher replies to the doubt
      - post:
          name: "Teacher replies to the doubt"
          url: "{{bffURL}}/internal-bff/api/v1/doubts/{{teacher_doubt_id}}/reply"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            doubt_id: "{{teacher_doubt_id}}"
            doubt_reply:
              text: "{{teacher_reply}}"
              images: []
              audios: []
          capture:
            - json: $.data
              as: teacher_reply_response
          expect:
            - statusCode: 200
            - hasProperty: "data.reply_id"
      - log: "Teacher replied to the doubt with ID: {{teacher_reply_response.reply_id}}"

      # Step 10: Student marks the doubt as resolved
      - post:
          name: "Student marks doubt as resolved"
          url: "{{bffURL}}/api/v1/v4/doubts/reply"
          headers:
            authorization: "Bearer {{login.studentToken}}"
            x-selected-batch-list: "{{testData.batch.id}}"
            x-selected-course-id: "{{testData.batch.course}}"
          json:
            doubt_id: "{{doubt_id}}"
            doubt_reply:
              text: "✅ Yes, got it"
              images: []
              audios: []
            meta: {}
            is_chip_selected: true
          capture:
            - json: $.data.reply_id
              as: resolution_reply_id
            - json: $.data.status
              as: final_status
          expect:
            - statusCode: 200
            - hasProperty: "data.reply_id"
      - log: "Student marked doubt as resolved with reply ID: {{resolution_reply_id}}, final status: {{final_status}}"
