# live-class-doubts.yml
# Flow - Teacher enables doubts in a live class, student submits a doubt, teacher resolves it
config:
  variables:
    doubtQuestion: "what is dc voltage?"
    subjectId: 2
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
      x-client-type: web
  fixtures:
    - bffURL
    - testData
    - login
      
scenarios:
  - name: "Live class doubts management from teacher side"
    flow:
      # Generate random class name and get dates
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiClass_"
          capture: random_class_name
      - function: 
          name: getDates
          capture: dates

      # Step 1: <PERSON><PERSON> creates a new online class schedule (using new API)
      - post:
          name: "Create online class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "data": {
                "type": "SCHEDULE_TYPE_CLASS",
                "schedules": [
                  {
                    "external_identifier": "0",
                    "title": "{{random_class_name}}",
                    "start_time": {{dates.meeting_start_epoch}},
                    "end_time": {{dates.meeting_end_epoch}},
                    "participants": [
                      {
                        "id": "{{ testData.teacher.id }}",
                        "type": "PARTICIPANT_TYPE_TEACHER",
                        "role": "PARTICIPANT_ROLE_ORGANIZER"
                      },
                      {
                        "id": "{{testData.batch.id}}",
                        "type": "PARTICIPANT_TYPE_BATCH",
                        "role": "PARTICIPANT_ROLE_VIEWER"
                      }
                    ],
                    "type": "SCHEDULE_TYPE_CLASS",
                    "facility_id": "",
                    "visibility": "SCHEDULE_VISIBILITY_PUBLIC",
                    "class_schedule_metadata": {
                      "class_type": "LIVE_LECTURE",
                      "mode": "SCHEDULE_MODE_ONLINE",
                      "scheduled_subjects": [
                        {
                          "id": "{{subjectId}}",
                          "subject_id": "{{subjectId}}",
                          "taxonomy_id": "{{testData.env.TAXONAMY_ID_LIVE}}",
                          "nodes": [
                            {
                              "id": "86",
                              "type": "TOPIC"
                            }
                          ]
                        }
                      ]
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.schedules[0].id
              as: captured_data_schedules_0_id
            - json: $.schedules[0].status
              as: captured_data_schedules_0_status
          expect:
            - statusCode: 200
            - hasProperty: "schedules[0].id"
      - log: "Status of scheduled class: {{captured_data_schedules_0_status}}"

      # Step 2: Admin publishes the online class (using new API)
      - put:
          name: "Publish online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "type": "SCHEDULE_TYPE_CLASS",
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "class_schedule": {
                "request": [
                  {
                    "from_status": "SCHEDULE_STATUS_DRAFT",
                    "to_status": "SCHEDULE_STATUS_PUBLISHED",
                    "schedule_time": {
                      "from": "",
                      "to": ""
                    },
                    "filter": {
                      "schedules": {
                        "values": [
                          "{{captured_data_schedules_0_id}}"
                        ],
                        "op": "IN"
                      }
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"

      - sleep: 10000 # Wait for meeting ID to be generated after publish
      
      # Step 3: Verify the online class is published and get meeting ID
      - post:
          name: "Verify online class is published"
          url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "filter": {
                "batches": {
                  "op": "IN",
                  "values": ["{{testData.batch.id}}"]
                },
                "schedule_time": {
                  "from": {{dates.meeting_start_epoch}},
                  "to": {{dates.meeting_end_epoch}}
                }
              },
              "pagination": {
                "page_size": 10,
                "page_number": 1,
                "sort": {
                  "by": "createdAt",
                  "order": "DESC"
                }
              }
            }
          capture:
            - json: $.data.schedules[0].meeting_id
              as: captured_data_schedules_0_details_class_id
            - json: $.data
              as: meeting_details_response
            - json: $.data.schedules[0].status
              as: schedule_status
            - json: $.data.schedules[0].mode
              as: class_mode
            - json: $.data.schedules[0].class_type
              as: class_type
          expect:
            - statusCode: 200
            - hasProperty: "data.schedules[0].meeting_id"
            - equals:
                - "{{schedule_status}}"
                - "SCHEDULE_STATUS_PUBLISHED"
            - equals:
                - "{{class_mode}}"
                - "SCHEDULE_MODE_ONLINE"
            - equals:
                - "{{class_type}}"
                - "LIVE_LECTURE"

      - log: "Meeting ID: {{captured_data_schedules_0_details_class_id}}"
      - sleep: 10000 # Wait for meeting to start

      # Step 4: Teacher joins the meeting
      - post:
          name: "Teacher joins the Meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_Teacher_join
            - json: $.data.role
              as: teacher_role
            - json: $.data.type
              as: teacher_type
            - json: $.data.room_id
              as: room_id
          expect:
            - statusCode: 200
            - hasProperty: "data.room_id"
            - hasProperty: "data.role"
            - hasProperty: "data.type"
            - hasProperty: "data.room_meta"
            - hasProperty: "data.permissions"
            - hasProperty: "data.configuration"
            - equals:
                - "{{teacher_role}}"
                - "TEACHER"
            - equals:
                - "{{teacher_type}}"
                - "ORGANISER"
            - equals:
                - "{{room_id}}"
                - "{{captured_data_schedules_0_details_class_id}}"

      - log: "Teacher join response: {{captured_data_Teacher_join}}"

      # Step 5: Student joins the meeting
      - post:
          name: "Student joins the Meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_student_join
            - json: $.data.role
              as: student_role
            - json: $.data.type
              as: student_type
            - json: $.data.room_id
              as: student_room_id
          expect:
            - statusCode: 200
            - hasProperty: "data.room_id"
            - hasProperty: "data.role"
            - hasProperty: "data.type"
            - hasProperty: "data.room_meta"
            - hasProperty: "data.permissions"
            - hasProperty: "data.configuration"
            - equals:
                - "{{student_role}}"
                - "STUDENT"
            - equals:
                - "{{student_type}}"
                - "AUDIENCE"
            - equals:
                - "{{student_room_id}}"
                - "{{captured_data_schedules_0_details_class_id}}"

      - log: "Student join response: {{captured_data_student_join}}"

      # Step 6: Student checks if doubts are enabled (should be disabled initially)
      - get:
          name: "Student checks if doubts are enabled"
          url: "{{bffURL}}/v1/in-class/doubt/meeting/{{captured_data_schedules_0_details_class_id}}?page=1&pageSize=10&status=OPEN&self=false&order=SORT_VOTES"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.enabled
              as: doubts_enabled_initial
            - json: $.doubtList
              as: initial_doubt_list
            - json: $.pageInfo
              as: initial_page_info
          expect:
            - statusCode: 200
            - hasProperty: "enabled"
            - hasProperty: "doubtList"
            - hasProperty: "pageInfo"
            - equals:
                - "{{doubts_enabled_initial}}"
                - false
            - hasProperty: "doubtList"
            - hasProperty: "pageInfo.page"
            - hasProperty: "pageInfo.hasNext"
            - hasProperty: "pageInfo.pageSize"

      - log: "Initial doubts enabled status: {{doubts_enabled_initial}}"
      - log: "Initial doubt list length: {{initial_doubt_list.length}}"

      # Step 7: Teacher enables doubts
      - post:
          name: "Teacher enables doubts"
          url: "{{bffURL}}/v1/in-class/doubt/meeting/{{captured_data_schedules_0_details_class_id}}?enable=true"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          expect:
            - statusCode: 200

      # Step 8: Teacher verifies doubts are enabled
      - get:
          name: "Teacher verifies doubts are enabled"
          url: "{{bffURL}}/v1/in-class/doubt/meeting/{{captured_data_schedules_0_details_class_id}}?page=1&pageSize=10&status=OPEN&self=false&order=SORT_VOTES"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          capture:
            - json: $.enabled
              as: doubts_enabled_teacher
          expect:
            - statusCode: 200
            - hasProperty: "enabled"
            - equals:
                - "{{doubts_enabled_teacher}}"
                - true

      - log: "Teacher side - doubts enabled: {{doubts_enabled_teacher}}"

      # Step 9: Student submits a doubt
      - post:
          name: "Student submits a doubt"
          url: "{{bffURL}}/v1/in-class/doubt"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            text: "{{doubtQuestion}} {{dates.currentTime_in_secs}}"
            meeting_id: "{{captured_data_schedules_0_details_class_id}}"
          capture:
            - json: $.doubtId
              as: doubt_id
          expect:
            - statusCode: 200
            - hasProperty: "doubtId"

      - log: "Created doubt with ID: {{doubt_id}}"

      # Add a longer sleep to ensure doubt is processed
      - sleep: 20000

      # Step 10: Teacher checks for open doubts
      - get:
          name: "Teacher checks for open doubts"
          url: "{{bffURL}}/v1/in-class/doubt/meeting/{{captured_data_schedules_0_details_class_id}}?page=1&pageSize=10&status=OPEN&self=false&order=SORT_VOTES"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          capture:
            - json: $.doubtList
              as: teacher_doubt_list
          expect:
            - statusCode: 200
            - hasProperty: "doubtList"

      - log: "Teacher doubt list: {{teacher_doubt_list}}"

      # Step 11: Teacher resolves the doubt using the original doubt ID
      - put:
          name: "Teacher resolves the doubt"
          url: "{{bffURL}}/v1/in-class/doubt/{{doubt_id}}"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            status: "RESOLVED"
          capture:
            - json: $.status
              as: resolved_status
          expect:
            - statusCode: 200
            - equals:
                - "{{resolved_status}}"
                - "RESOLVED"

      - log: "Doubt resolved with status: {{resolved_status}}"

      # Step 12: Student asks another doubt for deletion test
      - post:
          name: "Student asks another doubt for deletion test"
          url: "{{bffURL}}/v1/in-class/doubt"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            text: "what is dc voltage ?? {{dates.currentTime_in_secs}}"
            meeting_id: "{{captured_data_schedules_0_details_class_id}}"
          capture:
            - json: $.doubtId
              as: doubt_id_for_deletion
            - json: $.type
              as: doubt_type
            - json: $.status
              as: doubt_status_initial
            - json: $.profanity
              as: doubt_profanity
            - json: $.createdAt
              as: doubt_created_at
          expect:
            - statusCode: 200
            - hasProperty: "doubtId"
            - hasProperty: "type"
            - hasProperty: "status"
            - hasProperty: "profanity"
            - hasProperty: "createdAt"
            - equals:
                - "{{doubt_type}}"
                - "ACADEMIC"
            - equals:
                - "{{doubt_status_initial}}"
                - "OPEN"
            - equals:
                - "{{doubt_profanity}}"
                - false

      - log: "Created doubt for deletion with ID: {{doubt_id_for_deletion}}"

      # Step 13: Teacher deletes the doubt
      - put:
          name: "Teacher deletes the doubt"
          url: "{{bffURL}}/v1/in-class/doubt/{{doubt_id_for_deletion}}"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            status: "DELETED"
          capture:
            - json: $.doubtId
              as: deleted_doubt_id
            - json: $.status
              as: deleted_status
            - json: $.resolverType
              as: resolver_type
          expect:
            - statusCode: 200
            - hasProperty: "doubtId"
            - hasProperty: "status"
            - hasProperty: "resolverType"
            - equals:
                - "{{deleted_doubt_id}}"
                - "{{doubt_id_for_deletion}}"
            - equals:
                - "{{deleted_status}}"
                - "DELETED"
            - equals:
                - "{{resolver_type}}"
                - "TEACHER"

      - log: "Doubt deleted successfully with ID: {{deleted_doubt_id}} and status: {{deleted_status}}"

      # Step 14: Clean up - Student leaves the meeting
      - post:
          name: "Student leaves the meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          expect:
            - statusCode: 200

      # Step 13: Clean up - Teacher leaves the meeting
      - post:
          name: "Teacher leaves the meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          expect:
            - statusCode: 200

      # Step 14: Admin deletes the online class (using new API)
      - put:
          name: "Delete the online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "type": "SCHEDULE_TYPE_CLASS",
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "class_schedule": {
                "request": [
                  {
                    "from_status": "SCHEDULE_STATUS_PUBLISHED",
                    "to_status": "SCHEDULE_STATUS_DELETED",
                    "schedule_time": {
                      "from": "",
                      "to": ""
                    },
                    "filter": {
                      "schedules": {
                        "values": [
                          "{{captured_data_schedules_0_id}}"
                        ],
                        "op": "IN"
                      }
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.matched
              as: delete_matched_count
            - json: $.successCount
              as: delete_success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"
            
      - log: "Online class deleted successfully"
