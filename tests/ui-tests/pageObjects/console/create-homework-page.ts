import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { executionAsyncId } from 'async_hooks';
import { EnvUtils } from '../../../commons/env-utilities';


const CreateHomeworkPageUrl = '/paper/edit'
const currentEnv = EnvUtils.getInstance();

export class CreateHomeworkPage extends ICPage {

  readonly createHomeworkTitle: Locator;
  readonly scheduleText: Locator;
  readonly createHwSubtitleText: Locator;
  readonly classText: Locator;
  readonly chooseTopics: Locator;
  readonly addAnotherTopicOption: Locator;
  readonly chooseTopicDropdown: Locator;
  readonly selectTopicTitle: Locator;
  readonly resetOption: Locator;
  readonly searchForTopicsPlaceholder: Locator;
  readonly cancelButton: Locator;
  readonly applyButton: Locator;
  readonly atomicStructureTopic: Locator;
  readonly proceedButton: Locator;
  readonly sectionsSubTitle: Locator;
  readonly difficultyLevelText: Locator;
  readonly previouslyAssignedQs: Locator;
  readonly seletedTpoicTitle: Locator;
  readonly qsSelectedText: Locator;
  readonly clearAllOption: Locator;
  readonly dummyQuestion1: Locator;
  readonly dummyQuestion1CheckBox: Locator;
  readonly copyHomework: Locator;
  readonly firstQuestion: Locator;
  readonly oneSelectedTopicText: Locator;
  readonly clearAllSectionsOption: Locator;
  readonly thisWillClearText: Locator;
  readonly confirmButton: Locator;
  readonly allSectionsClearedTost: Locator;
  readonly previewButton: Locator;
  readonly qsCannotEditText: Locator;
  readonly editHomework: Locator;
  readonly publishHomeWork: Locator;
  readonly publishToTitle: Locator;
  readonly deadlineSubtitle: Locator;
  readonly publishToOtherClasses: Locator;
  readonly homeworkScheduled: Locator;
  readonly homeworkWillBePublishedText: Locator;
  readonly homeworkPublishedText: Locator;
  readonly doneButton: Locator;
  readonly homeWorkFuturePublish: Locator;
  readonly chooseSubTopicDropdown: Locator;
  readonly atomicStructureSubTopic: Locator;
  readonly homeWorkPublishButton: Locator;
  readonly studentsWillbeNotifiedText: Locator;
  readonly hwQuestion2CheckBox: Locator;
  readonly hwQuestion3CheckBox: Locator;
  readonly hwQuestion4CheckBox: Locator;
  readonly basicMoleConceptTopic: Locator;
  readonly subTopicCheck: Locator;
  readonly selectAll: Locator;
  readonly hwTimeDropdown: Locator;
  readonly firstTimeOption: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, CreateHomeworkPageUrl, isMobile);
    this.createHomeworkTitle = page.getByText('Create homework');
    this.scheduleText = page.getByText('Schedule');
    this.createHwSubtitleText = page.getByText('Create HW');
    this.classText = page.getByText('Class -');
    this.chooseTopics = page.getByText('Choose topics');
    this.addAnotherTopicOption = page.getByText('Add another topic');
    this.chooseTopicDropdown = page.getByText('Choose topic', { exact: true });
    this.selectTopicTitle = page.getByText('SELECT TOPIC');
    this.resetOption = page.getByRole('button', { name: 'Reset' });
    this.searchForTopicsPlaceholder = page.getByPlaceholder('Search for topics ');
    this.cancelButton = isMobile ? page.getByRole('button', { name: 'Cancel' }).nth(1) : page.getByRole('button', { name: 'Cancel' });
    this.applyButton = page.getByRole('button', { name: 'Apply' });
    this.atomicStructureTopic = page.getByText('Atomic Structure');
    this.basicMoleConceptTopic = page.getByText('Basic Mole Concept')
    this.proceedButton = page.getByRole('button', { name: 'Proceed' });
    this.sectionsSubTitle = isMobile ? page.locator('span').filter({ hasText: 'Sections' }) : page.getByText('Sections');
    this.difficultyLevelText = isMobile ? page.locator('span').filter({ hasText: 'Difficulty level' }) : page.getByText('Difficulty level');
    this.previouslyAssignedQs = isMobile ? page.locator('span').filter({ hasText: 'Previously assigned qs ' }) : page.getByText('Previously assigned qs ');
    this.seletedTpoicTitle = page.getByTestId('tabGroup');
    this.clearAllOption = page.getByText('Clear All', { exact: true });
    this.qsSelectedText = page.getByText('Qs selected');
    this.dummyQuestion1 = page.locator('div').filter({ hasText: /Dummy_|EXERCISE-|Extra Questions, / }).nth(1);
    this.dummyQuestion1CheckBox = page.locator("((//*[contains(@id,'Dummy_') or contains(@id,'EXERCISE-') or contains(@id,'Extra-Questions-')])[1]//*)[3]");
    this.copyHomework = page.getByText('Copy Homework');
    this.firstQuestion = page.locator('.hw-checkmark').first();
    this.oneSelectedTopicText = page.getByText('1 selected');
    this.clearAllSectionsOption = page.getByText('Clear all selections?');
    this.thisWillClearText = page.getByText('This will clear all your');
    this.confirmButton = page.getByRole('button', { name: 'Confirm' });
    this.allSectionsClearedTost = page.getByText('All selection cleared');
    this.previewButton = page.getByTestId('cta-button').getByText('Preview');
    this.qsCannotEditText = isMobile ? page.getByText('Qs cannot be edited after').nth(1) : page.getByText('Qs cannot be edited after').first();
    this.editHomework = page.getByRole('button', { name: 'Edit Homework' });
    this.publishHomeWork = isMobile? page.getByRole('button', { name: 'Publish' }).first() : page.getByRole('button', { name: 'Publish' });
    this.publishToTitle = page.getByText('Publish to -');
    this.deadlineSubtitle = page.getByText('Deadline');
    this.publishToOtherClasses = page.getByText('Publish to other classes');
    this.homeworkScheduled = page.getByText('Homework scheduled');
    this.homeworkWillBePublishedText = page.getByText('Homework will be published');
    this.doneButton = page.getByRole('button', { name: 'Done' });
    this.homeWorkFuturePublish = page.locator('div').filter({ hasText: /^Homework will be scheduled for future classesPublish$/ }).getByTestId('cta-button');
    this.homeWorkPublishButton = page.getByTestId('cta-button').nth(2).getByText('publish');
    this.chooseSubTopicDropdown = page.getByText('Choose sub-topic', { exact: true });
    this.atomicStructureSubTopic = page.getByText('Bohr\'s Atomic Model');
    this.homeworkPublishedText = page.getByText('Homework published');
    this.studentsWillbeNotifiedText = page.getByText('Students will be notified');
    this.hwQuestion2CheckBox = page.locator("((//*[contains(@id,'Dummy_') or contains(@id,'EXERCISE-') or contains(@id,'Extra-Questions-')])[2]//*)[3]");
    this.hwQuestion3CheckBox = page.locator("((//*[contains(@id,'Dummy_') or contains(@id,'EXERCISE-') or contains(@id,'Extra-Questions-')])[3]//*)[3]");
    this.hwQuestion4CheckBox = page.locator("((//*[contains(@id,'Dummy_') or contains(@id,'EXERCISE-') or contains(@id,'Extra-Questions-')])[4]//*)[3]");
    this.subTopicCheck = page.locator('.hw-checkmark');
    this.selectAll = page.getByRole('button', { name: 'Select all' });
    this.hwTimeDropdown = page.getByTestId('hw-time-dropdown');
    this.firstTimeOption = page.locator('.p-3.w-full-available.border-b.border-inactive').first();
  }

  async createHomework() {
    await slowExpect(this.createHomeworkTitle, 'Verify create homework title is visible').toBeVisible();

    if (!this.isMobile) {
      await expect(this.scheduleText, 'Verify schedule text is visible').toBeVisible();
    }

    await expect(this.classText, 'Verify class text is visible').toBeVisible();
    await expect(this.chooseTopics, 'Verify choose Topics subtitle is visible').toBeVisible();
    await expect(this.addAnotherTopicOption, 'Verify add Another Topic Option is visible').toBeVisible();
    await expect(this.proceedButton, 'Verify proceed button is disabled').toBeDisabled();
    await expect(this.chooseTopicDropdown, 'Verify choose Topic dropdown is visible').toBeVisible();
    await this.chooseTopicDropdown.click();
    await expect(this.selectTopicTitle, 'Verify select topic title is visible').toBeVisible();
    await expect(this.resetOption, 'Verify reset option is visible').toBeVisible();
    await expect(this.searchForTopicsPlaceholder, 'Verify search for topics placeholder is visible').toBeVisible();
    await expect(this.cancelButton, 'Verify cancel button is visible').toBeVisible();
    await expect(this.applyButton, 'Verify apply button is visible').toBeVisible();
    await expect(this.atomicStructureTopic, 'Verify atomic structure topic is visible').toBeVisible();
    await this.atomicStructureTopic.check();
    await expect(this.atomicStructureTopic, 'Verify atomic structure topic is checked radium button').toBeChecked();
    await this.resetOption.click();

    if (currentEnv.isProd()) {
      await expect(this.atomicStructureTopic, 'Verify atomic structure topic is not checked radium button').not.toBeChecked();
      await this.atomicStructureTopic.check();
      await expect(this.atomicStructureTopic, 'Verify atomic structure topic is checked').toBeChecked();
    }
    else {
      await expect(this.basicMoleConceptTopic, 'Verify basic mole concept topic is not checked radium button').not.toBeChecked();
      await this.basicMoleConceptTopic.check();
      await expect(this.basicMoleConceptTopic, 'Verify basic mole concept topic is checked').toBeChecked();
    }

    await this.applyButton.click();

    await expect(this.chooseSubTopicDropdown, 'Verify choose Sub Topic dropdown is visible').toBeVisible();
    await this.chooseSubTopicDropdown.click();

    if(!await this.subTopicCheck.isChecked()) {
      await expect(this.selectAll, 'Verify Select All button is visible').toBeVisible();
      await this.selectAll.click();
    }

    await expect(this.applyButton, 'Verify Apply button is visible').toBeVisible();
    await expect(this.applyButton, 'Verify Apply button is enabled').toBeEnabled();
    await this.applyButton.click();

    await this.page.waitForTimeout(1000);
    await slowExpect(this.proceedButton, 'Verify proceed button is enabled').toBeEnabled();
    await this.proceedButton.click();

    await slowExpect(this.sectionsSubTitle, 'Verify sections sub title is visible').toBeVisible();
    await expect(this.difficultyLevelText, 'Verify difficulty level text is visible').toBeVisible();
    await expect(this.previouslyAssignedQs, 'Verify previously assigned questions option is visible').toBeVisible();
    await expect(this.clearAllOption, 'Verify cleared all option is visible').toBeVisible();
    await expect(this.copyHomework, 'Verify copy homework option is visible').toBeVisible();
    if (!(await this.dummyQuestion1.isVisible())) {
      await this.previouslyAssignedQs.click();
      await expect(this.dummyQuestion1, 'Verify dummy question 1 is visible').toBeVisible();
      await this.dummyQuestion1CheckBox.check();
    } else {
      await expect(this.dummyQuestion1, 'Verify dummy question 1 is visible').toBeVisible();
      await this.dummyQuestion1CheckBox.check();
    }
    await expect(this.previewButton, "Verify preview button is visible").toBeVisible();
    await expect(this.previewButton, "Verify preview button is enabled").toBeEnabled();
    await this.clearAllOption.click();

    if (EnvUtils.getInstance().isProd() && this.isMobile) {
      await this.page.evaluate(() => {
        document.body.style.transform = 'scale(0.5)';
        document.body.style.transformOrigin = '0 0';
        document.body.style.width = '200%';
      });
    }

    await expect(this.clearAllSectionsOption, "Verify clear all sections title is visible").toBeVisible();
    await expect(this.thisWillClearText, "Verify this will clear all sections subtitle is visible").toBeVisible();
    await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
    await expect(this.confirmButton, "Verify confirm button is visible").toBeVisible();
    await this.confirmButton.click();
    await expect(this.allSectionsClearedTost, "Verify all cleared sections tost message is visible").toBeVisible();
    await expect(this.dummyQuestion1CheckBox, 'Verify dummy question 1 is visible').not.toBeChecked();
    await this.dummyQuestion1CheckBox.check();
    await expect(this.hwQuestion2CheckBox, 'Verify dummy question 2 is visible').not.toBeChecked();
    await this.hwQuestion2CheckBox.check();
    await this.page.waitForTimeout(1000); // wait is required
    // await this.hwQuestion4CheckBox.scrollIntoViewIfNeeded({ timeout: 30000 });
    await expect(this.hwQuestion3CheckBox, 'Verify dummy question 3 is visible').toBeVisible();
    await expect(this.hwQuestion3CheckBox, 'Verify dummy question 3 is visible').not.toBeChecked();
    await this.hwQuestion3CheckBox.check();
    await expect(this.previewButton, "Verify preview button is enabled").toBeEnabled();
    await this.previewButton.click();
  }

  async publishTheHomework() {
    await slowExpect(this.qsCannotEditText, "Verify questions cannot be edited after clicking publish text is visible").toBeVisible();
    await expect(this.editHomework, "Verify edit homework button is visible").toBeVisible();
    await expect(this.publishHomeWork, "Verify publish homework button is visible").toBeVisible();
    await this.publishHomeWork.click();
    await slowExpect(this.publishToTitle, "Verify publish to title is visible").toBeVisible();
    await expect(this.deadlineSubtitle, "Verify deadline sub title is visible").toBeVisible();
    await expect(this.hwTimeDropdown, "Verify homework time dropdown is visible").toBeVisible();
    await this.hwTimeDropdown.click();
    await expect(this.firstTimeOption, "Verify first time option is visible").toBeVisible();
    await this.firstTimeOption.click();
    await expect(this.publishToOtherClasses, "Verify publish to another class is visible").toBeVisible();
    await expect(this.homeWorkFuturePublish, "Verify publish homework button is visible").toBeVisible();
    await this.homeWorkFuturePublish.click();
    await slowExpect(this.homeworkScheduled, "Verify homework scheduled is visible").toBeVisible();
    await expect(this.homeworkWillBePublishedText, "Verify homework will be published is visible").toBeVisible();
    await expect(this.doneButton, "Verify done button is visible").toBeVisible();
    await this.doneButton.click();
  }

  async publishTheHomeworkForPastSchedule() {
    await slowExpect(this.qsCannotEditText, "Verify questions cannot be edited after clicking publish text message is visible").toBeVisible();
    await expect(this.editHomework, "Verify edit homework button is visible").toBeVisible();
    await expect(this.publishHomeWork, "Verify publish homework button is visible").toBeVisible();
    await this.publishHomeWork.click();
    await slowExpect(this.publishToTitle, "Verify publish to title is visible").toBeVisible();
    await expect(this.deadlineSubtitle, "Verify deadline sub title is visible").toBeVisible();
    await expect(this.publishToOtherClasses, "Verify publish to another class is visible").toBeVisible();
    await expect(this.homeWorkPublishButton, "Verify publish homework button is visible").toBeVisible();
    await this.homeWorkPublishButton.click();
    await slowExpect(this.homeworkPublishedText, "Verify homework scheduled is visible").toBeVisible();
    await expect(this.studentsWillbeNotifiedText, "Verify homework will be published is visible").toBeVisible();
    await expect(this.doneButton, "Verify done button is visible").toBeVisible();
    await this.doneButton.click();
  }

}


