import { test, expect, customExpect, slowExpect } from '../fixtures/ui-fixtures';
import { StringUtils, PlaywrightUtils } from '../../commons/common-functions';
import { format } from 'path';
import { EnvUtils } from '../../commons/env-utilities';

test.describe('URM UI Tests', {
    tag: ['@URM', '@ui']
}, () => {
    /** 
     * @info Test Naming convention updated and assertion message
     */
    const currentEnv = EnvUtils.getInstance()
    if (currentEnv.isStage()) {
        test('Verify student management ', async ({ adminPage, testData }) => {

            /* Verify and update student details by direct navigation */
            await test.step(`Verify and update student details by direct navigation`, async () => {
                await adminPage.pos.studentManagementPage.validateAndUpdateStudentDetails(testData.student.id);
            });

        });

    }

    test('Verify admin able to add/view/delete syllabus to course', async ({ adminPage, testData }) => {
        const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
        const currentDate = StringUtils.getCurrentTimeWithOffset("DD-MM-YYYY", 0, false, timezone);
        const endDate = StringUtils.getCurrentTimeWithOffset("DD-MM-", 0, false, timezone) + '2026';
        const randomFourDigit = StringUtils.generateRandomFourDigits();
        const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : "";
        const courseName = process.env.SYLLABUS_TESTING_COURSE ? process.env.SYLLABUS_TESTING_COURSE : "";
        const languageName = "English"
        /* Navigate to course & syllabus managent page */
        await test.step(`Navigate to course & syllabus managent page`, async () => {
            await adminPage.pos.teacherHomePage.navigateToInternalUser();
            await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage();
            await adminPage.waitForLoadState('networkidle');
            await expect(adminPage).toHaveURL(/.*course-syllabus*/);
        });

        /* Verify and apply filters for center */
        await test.step(`Verify and apply filters for center`, async () => {
            await adminPage.pos.courseAndSyllabusPage.validateAndApplyCourseFilter(centerName, courseName, languageName);
            await slowExpect(adminPage.pos.courseAndSyllabusPage.apiLoader, "Verify page loading").not.toBeVisible();
        });

        /* Add syllabus to course and validate */
        await test.step(`Add syllabus to course and validate`, async () => {
            await adminPage.pos.courseAndSyllabusPage.addSyllabusToCourseAndValidate();
        });

        /* Verify and delete added syllabus to course */
        await test.step(`Verify and delete created phase`, async () => {
            await adminPage.pos.courseAndSyllabusPage.validateAndApplyCourseFilter(centerName, courseName, languageName);
            await slowExpect(adminPage.pos.courseAndSyllabusPage.apiLoader, "Verify page loading").not.toBeVisible();
            await adminPage.pos.courseAndSyllabusPage.verifyAndDeleteSyllabusToCourse();
            await slowExpect(adminPage.pos.courseAndSyllabusPage.apiLoader, "Verify page loading").not.toBeVisible();
        });

    });

});
